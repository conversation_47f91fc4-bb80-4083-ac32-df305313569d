# 失败任务重试功能优化

## 优化概述

针对 `retryFailedTasks` 方法进行了数据库查询优化，通过在数据库层面过滤可重试的任务，减少不必要的数据传输和内存处理。

## 优化前后对比

### 优化前
```java
// 查询所有失败的任务
List<TaskAllocation> failedTasks = allocationRepository.findByTaskStatus(TaskStatus.ERROR);

for (TaskAllocation failedTask : failedTasks) {
    // 在应用层检查是否可以重试
    if (!failedTask.canRetry(MAX_RETRY_COUNT)) {
        continue; // 跳过不可重试的任务
    }
    // 处理可重试的任务...
}
```

**问题**：
- 查询返回所有ERROR状态的任务，包括已达到最大重试次数的任务
- 在应用层过滤，浪费网络传输和内存
- 随着失败任务积累，查询效率逐渐降低

### 优化后
```java
// 直接查询可重试的失败任务
List<TaskAllocation> retryableTasks = allocationRepository.findRetryableFailedTasks(MAX_RETRY_COUNT);

for (TaskAllocation failedTask : retryableTasks) {
    // 双重检查（防止并发修改）
    if (!failedTask.canRetry(MAX_RETRY_COUNT)) {
        continue;
    }
    // 处理可重试的任务...
}
```

**优势**：
- 数据库层面过滤，只返回真正需要处理的任务
- 减少网络传输和内存占用
- 查询效率不受失败任务总数影响

## 新增查询方法

### findRetryableFailedTasks
```java
@Query("{'taskStatus': 'ERROR', $or: [{'retryCount': {$lt: ?0}}, {'retryCount': null}]}")
List<TaskAllocation> findRetryableFailedTasks(int maxRetryCount);
```

**查询条件解释**：
- `taskStatus: 'ERROR'`: 只查询失败状态的任务
- `retryCount: {$lt: maxRetryCount}`: 重试次数小于最大重试次数
- `retryCount: null`: 包含新创建的失败任务（retryCount为null）

## 性能提升分析

### 场景假设
- 系统中有1000个失败任务
- 其中800个已达到最大重试次数，200个可以重试

### 优化前
- 查询返回：1000个任务
- 网络传输：1000个任务的完整数据
- 内存处理：遍历1000个任务，过滤出200个
- 实际处理：200个任务

### 优化后
- 查询返回：200个任务
- 网络传输：200个任务的完整数据
- 内存处理：遍历200个任务
- 实际处理：200个任务

### 性能提升
- **网络传输减少**：80%（从1000个减少到200个）
- **内存占用减少**：80%
- **CPU处理减少**：80%（减少了800次无效的canRetry检查）

## 数据库索引建议

为了进一步优化查询性能，建议在以下字段上创建复合索引：

```javascript
// MongoDB索引
db.task_allocation.createIndex({
    "taskStatus": 1,
    "retryCount": 1
});
```

## 兼容性说明

### 向后兼容
- 保留了原有的 `findByTaskStatus` 方法
- 新增的 `retryCount` 字段默认值为0
- 对于历史数据，retryCount为null时被视为可重试

### 数据迁移
对于已存在的ERROR状态任务，如果需要准确的重试控制，可以执行以下数据迁移：

```javascript
// 为历史失败任务初始化retryCount
db.task_allocation.updateMany(
    {
        "taskStatus": "ERROR",
        "retryCount": null
    },
    {
        $set: {"retryCount": 0}
    }
);
```

## 监控指标

优化后可以通过以下指标监控重试功能的效果：

1. **可重试任务数量**：`findRetryableFailedTasks` 返回的任务数
2. **重试成功率**：重试成功任务数 / 总重试任务数
3. **查询响应时间**：数据库查询耗时
4. **内存使用情况**：重试过程中的内存占用

## 测试覆盖

新增的测试用例覆盖了以下场景：
- 优化查询只返回可重试任务
- 没有可重试任务的情况
- 并发修改的双重检查机制
- retryCount为null的新失败任务
- 性能对比验证

## 总结

通过数据库查询优化，`retryFailedTasks` 方法的性能得到显著提升：
- ✅ 减少了不必要的数据传输
- ✅ 降低了内存占用
- ✅ 提高了处理效率
- ✅ 保持了向后兼容性
- ✅ 增加了完整的测试覆盖

这个优化特别适用于有大量历史失败任务的生产环境，能够有效提升系统的整体性能。
