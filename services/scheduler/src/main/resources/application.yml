server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: cv-scheduler
  
  data:
    mongodb:
      uri: ${MONGODB_URI:*************************************************************************}
      database: ${MONGODB_DATABASE:cv_scheduler}
      auto-index-creation: true
      
  # Redis配置 - 当使用分布式锁时启用
  # redis:
  #   host: ${REDIS_HOST:localhost}
  #   port: ${REDIS_PORT:6379}
  #   password: ${REDIS_PASSWORD:}
  #   timeout: 2000ms
  #   lettuce:
  #     pool:
  #       max-active: 8
  #       max-idle: 8
  #       min-idle: 0

scheduler:
  # 锁类型配置: local(本地内存锁) 或 redis(分布式锁)
  lock:
    type: local  # 默认使用本地锁，适用于单例部署

  health-check:
    interval: 30000  # 30秒
    timeout: 5000    # 5秒
    enabled: true
    recovery-interval: 60000  # 60秒

  strategy:
    default-mode: FILL_FIRST
    enable-dynamic-schedule: true
    rebalance-threshold: 80  # 负载超过80%时触发重平衡

  service:
    discovery:
      enabled: true
      interval: 60000  # 60秒

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

logging:
  config: classpath:logback-spring.xml
  level:
    com.bohua.scheduler: INFO
    org.springframework.web: INFO
    org.springframework.data.mongodb: INFO
    org.mongodb.driver: WARN
    org.apache.http: WARN
    org.apache.hc: WARN
    io.micrometer: WARN
