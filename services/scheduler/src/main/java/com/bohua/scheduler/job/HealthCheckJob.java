package com.bohua.scheduler.job;

import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import com.bohua.scheduler.service.HealthCheckService;
import com.bohua.scheduler.service.ServiceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 健康检查定时任务
 * 负责定期检查推理服务的健康状态和恢复不健康的服务
 */
@Component
@Slf4j
public class HealthCheckJob {
    
    @Autowired
    private InferenceServiceRepository serviceRepository;
    
    @Autowired
    private ServiceManager serviceManager;
    
    @Autowired
    private HealthCheckService healthCheckService;
    
    @Value("${scheduler.health-check.enabled:true}")
    private boolean healthCheckEnabled;
    
    /**
     * 定时健康检查
     * 每30秒执行一次，检查所有活跃服务的健康状态
     */
    @Scheduled(fixedDelayString = "${scheduler.health-check.interval:30000}")
    public void performHealthCheck() {
        if (!healthCheckEnabled) {
            return;
        }
        
        log.debug("开始执行健康检查");
        
        List<InferenceService> services = serviceRepository.findByStatus(ServiceStatus.ACTIVE);
        
        for (InferenceService service : services) {
            try {
                boolean isHealthy = healthCheckService.checkServiceHealth(service);
                if (!isHealthy) {
                    log.warn("服务健康检查失败，标记为不可用: serviceId={}, serviceName={}", 
                           service.getServiceId(), service.getServiceName());
                    serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.INACTIVE);
                }
            } catch (Exception e) {
                log.error("健康检查异常: serviceId={}, error={}", 
                         service.getServiceId(), e.getMessage());
                serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.INACTIVE);
            }
        }
        
        log.debug("健康检查完成，检查了{}个服务", services.size());
    }
    
    /**
     * 恢复不健康的服务
     * 每60秒执行一次，尝试恢复标记为不可用的服务
     */
    @Scheduled(fixedDelayString = "${scheduler.health-check.recovery-interval:60000}")
    public void recoverUnhealthyServices() {
        if (!healthCheckEnabled) {
            return;
        }
        
        log.debug("开始尝试恢复不健康的服务");
        
        List<InferenceService> inactiveServices = serviceRepository.findByStatus(ServiceStatus.INACTIVE);
        
        int recoveredCount = 0;
        for (InferenceService service : inactiveServices) {
            try {
                boolean isHealthy = healthCheckService.checkServiceHealth(service);
                if (isHealthy) {
                    log.info("服务恢复健康，重新激活: serviceId={}, serviceName={}", 
                           service.getServiceId(), service.getServiceName());
                    serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.ACTIVE);
                    recoveredCount++;
                }
            } catch (Exception e) {
                log.debug("服务恢复检查失败: serviceId={}, error={}", 
                         service.getServiceId(), e.getMessage());
            }
        }
        
        log.debug("服务恢复检查完成，检查了{}个不健康服务，恢复了{}个服务", 
                inactiveServices.size(), recoveredCount);
    }
}
