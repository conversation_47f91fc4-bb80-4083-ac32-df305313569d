package com.bohua.scheduler.service;

import com.bohua.scheduler.dto.HealthResponse;
import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.time.Duration;

/**
 * 健康检查服务
 * 提供服务健康检查的核心功能，定时任务已迁移到 HealthCheckJob
 */
@Service
@Slf4j
public class HealthCheckService {
    
    @Autowired
    private InferenceServiceRepository serviceRepository;

    @Autowired
    private ServiceManager serviceManager;

    @Value("${scheduler.health-check.timeout:5000}")
    private int healthCheckTimeout;

    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        // 配置RestTemplate
        restTemplate = new RestTemplateBuilder()
            .setConnectTimeout(Duration.ofMillis(healthCheckTimeout))
            .setReadTimeout(Duration.ofMillis(healthCheckTimeout))
            .build();

        log.info("健康检查服务初始化完成: timeout={}ms", healthCheckTimeout);
    }

    
    /**
     * 检查单个服务健康状态
     */
    public boolean checkServiceHealth(InferenceService service) {
        try {
            String healthUrl = buildHealthUrl(service.getBaseUrl());
            
            log.debug("检查服务健康状态: serviceId={}, url={}", service.getServiceId(), healthUrl);
            
            ResponseEntity<HealthResponse> response = restTemplate.getForEntity(
                healthUrl, HealthResponse.class);
            
            boolean isHealthy = response.getStatusCode().is2xxSuccessful() && 
                               response.getBody() != null && 
                               "UP".equalsIgnoreCase(response.getBody().getStatus());
            
            if (isHealthy) {
                log.debug("服务健康检查通过: serviceId={}", service.getServiceId());
                // 更新服务心跳时间
                serviceManager.updateServiceHeartbeat(service.getServiceId());
            } else {
                log.warn("服务健康检查失败: serviceId={}, status={}",
                        service.getServiceId(),
                        response.getBody() != null ? response.getBody().getStatus() : "null");
            }

            return isHealthy;
            
        } catch (Exception e) {
            log.debug("服务健康检查异常: serviceId={}, error={}", 
                     service.getServiceId(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 手动检查服务健康状态
     */
    public HealthResponse manualHealthCheck(String serviceId) {
        InferenceService service = serviceRepository.findById(serviceId)
            .orElseThrow(() -> new RuntimeException("服务不存在"));
        
        try {
            String healthUrl = buildHealthUrl(service.getBaseUrl());
            ResponseEntity<HealthResponse> response = restTemplate.getForEntity(
                healthUrl, HealthResponse.class);
            
            return response.getBody();
            
        } catch (Exception e) {
            return HealthResponse.builder()
                .status("DOWN")
                .timestamp(System.currentTimeMillis())
                .build();
        }
    }

    
    /**
     * 构建健康检查URL
     */
    private String buildHealthUrl(String baseUrl) {
        String url = baseUrl.endsWith("/") ? baseUrl : baseUrl + "/";
        return url + "health";
    }
}
