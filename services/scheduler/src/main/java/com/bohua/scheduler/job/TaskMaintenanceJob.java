package com.bohua.scheduler.job;

import com.bohua.scheduler.service.SchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 任务维护定时任务
 * 负责定期维护任务状态，包括重试失败任务和清理孤儿任务
 */
@Component
@Slf4j
public class TaskMaintenanceJob {
    
    @Autowired
    private SchedulerService schedulerService;
    
    /**
     * 定时任务维护
     * 每30秒执行一次，进行失败任务重试和孤儿任务清理
     */
    @Scheduled(fixedRate = 30000) // 30秒
    public void scheduledTaskMaintenance() {
        try {
            log.debug("开始定时任务维护");
            
            // 重试失败的任务
            schedulerService.retryFailedTasks();
            
            // 清理孤儿任务
            schedulerService.cleanupOrphanTasks();
            
            log.debug("定时任务维护完成");
        } catch (Exception e) {
            log.error("定时任务维护异常: {}", e.getMessage(), e);
        }
    }
}
