package com.bohua.scheduler.service;

import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.SimplifiedAtomicTask;
import com.bohua.scheduler.model.TaskAllocation;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.model.enums.TaskStatus;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import com.bohua.scheduler.repository.TaskAllocationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 优化后的失败任务重试功能测试
 */
@ExtendWith(MockitoExtension.class)
class RetryFailedTasksOptimizedTest {

    @Mock
    private TaskAllocationRepository allocationRepository;

    @Mock
    private InferenceServiceRepository serviceRepository;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private SchedulerService schedulerService;

    private TaskAllocation retryableTask;
    private TaskAllocation maxRetriesTask;
    private InferenceService availableService;

    @BeforeEach
    void setUp() {
        // 创建测试用的可重试任务
        SimplifiedAtomicTask taskRequest = SimplifiedAtomicTask.builder()
                .taskId("test-task-001")
                .taskName("测试任务")
                .build();

        retryableTask = TaskAllocation.builder()
                .allocationId("alloc-001")
                .taskId("test-task-001")
                .serviceId("service-001")
                .taskStatus(TaskStatus.ERROR)
                .errorMessage("初始失败")
                .retryCount(1) // 已重试1次，还可以重试2次
                .taskRequest(taskRequest)
                .build();

        // 创建已达到最大重试次数的任务
        maxRetriesTask = TaskAllocation.builder()
                .allocationId("alloc-002")
                .taskId("test-task-002")
                .serviceId("service-001")
                .taskStatus(TaskStatus.ERROR)
                .errorMessage("已达到最大重试次数")
                .retryCount(3) // 已重试3次，不能再重试
                .taskRequest(taskRequest)
                .build();

        // 创建可用的推理服务
        availableService = InferenceService.builder()
                .serviceId("service-001")
                .serviceName("测试服务")
                .baseUrl("http://localhost:8081")
                .status(ServiceStatus.ACTIVE)
                .currentQuota(0)
                .maxQuota(10)
                .build();
    }

    @Test
    void testOptimizedQuery_OnlyRetryableTasks() {
        // 测试优化查询：只返回可重试的任务
        when(allocationRepository.findRetryableFailedTasks(3))
                .thenReturn(Arrays.asList(retryableTask)); // 只返回可重试的任务
        when(serviceRepository.findById("service-001"))
                .thenReturn(Optional.of(availableService));

        // 模拟任务发送成功
        when(restTemplate.exchange(any(), any(), any(), eq(String.class)))
                .thenReturn(null);

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证只查询了可重试的任务
        verify(allocationRepository, times(1)).findRetryableFailedTasks(3);
        verify(allocationRepository, never()).findByTaskStatus(TaskStatus.ERROR);

        // 验证任务状态更新
        verify(allocationRepository, times(1)).save(argThat(task -> 
                task.getTaskStatus() == TaskStatus.RUNNING &&
                task.getRetryCount() == 2 && // 重试次数增加到2
                task.getErrorMessage() == null
        ));
    }

    @Test
    void testOptimizedQuery_NoRetryableTasks() {
        // 测试没有可重试任务的情况
        when(allocationRepository.findRetryableFailedTasks(3))
                .thenReturn(Collections.emptyList());

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证没有进行任何重试操作
        verify(serviceRepository, never()).findById(any());
        verify(restTemplate, never()).exchange(any(), any(), any(), any());
        verify(allocationRepository, never()).save(any());
    }

    @Test
    void testDoubleCheck_ConcurrentModification() {
        // 模拟并发修改：查询时任务可重试，但处理时已达到最大重试次数
        TaskAllocation concurrentModifiedTask = TaskAllocation.builder()
                .allocationId("alloc-003")
                .taskId("test-task-003")
                .serviceId("service-001")
                .taskStatus(TaskStatus.ERROR)
                .retryCount(3) // 在处理过程中被其他线程修改为最大重试次数
                .build();

        when(allocationRepository.findRetryableFailedTasks(3))
                .thenReturn(Arrays.asList(concurrentModifiedTask));

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证双重检查生效，跳过了这个任务
        verify(serviceRepository, never()).findById(any());
        verify(restTemplate, never()).exchange(any(), any(), any());
        verify(allocationRepository, never()).save(any());
    }

    @Test
    void testQueryPerformance_Comparison() {
        // 这个测试展示了优化前后的差异
        
        // 优化前：查询所有ERROR状态的任务（包括不可重试的）
        // List<TaskAllocation> allFailedTasks = allocationRepository.findByTaskStatus(TaskStatus.ERROR);
        // 然后在代码中过滤可重试的任务
        
        // 优化后：直接查询可重试的任务
        when(allocationRepository.findRetryableFailedTasks(3))
                .thenReturn(Arrays.asList(retryableTask));
        when(serviceRepository.findById("service-001"))
                .thenReturn(Optional.of(availableService));
        when(restTemplate.exchange(any(), any(), any(), eq(String.class)))
                .thenReturn(null);

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证使用了优化的查询方法
        verify(allocationRepository, times(1)).findRetryableFailedTasks(3);
        
        // 验证没有使用旧的查询方法
        verify(allocationRepository, never()).findByTaskStatus(TaskStatus.ERROR);
    }

    @Test
    void testRetryableTasksQuery_NullRetryCount() {
        // 测试retryCount为null的情况（新创建的失败任务）
        TaskAllocation newFailedTask = TaskAllocation.builder()
                .allocationId("alloc-004")
                .taskId("test-task-004")
                .serviceId("service-001")
                .taskStatus(TaskStatus.ERROR)
                .retryCount(null) // 新任务，retryCount为null
                .build();

        when(allocationRepository.findRetryableFailedTasks(3))
                .thenReturn(Arrays.asList(newFailedTask));
        when(serviceRepository.findById("service-001"))
                .thenReturn(Optional.of(availableService));
        when(restTemplate.exchange(any(), any(), any(), eq(String.class)))
                .thenReturn(null);

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证null retryCount的任务也被正确处理
        verify(allocationRepository, times(1)).save(argThat(task -> 
                task.getTaskStatus() == TaskStatus.RUNNING &&
                task.getRetryCount() == 1 // 从null变为1
        ));
    }

    @Test
    void testTaskAllocation_CanRetryMethod() {
        // 测试TaskAllocation的canRetry方法
        TaskAllocation task = new TaskAllocation();
        
        // 测试null retryCount
        task.setRetryCount(null);
        assert task.canRetry(3) == true;
        
        // 测试0次重试
        task.setRetryCount(0);
        assert task.canRetry(3) == true;
        
        // 测试未达到最大重试次数
        task.setRetryCount(2);
        assert task.canRetry(3) == true;
        
        // 测试达到最大重试次数
        task.setRetryCount(3);
        assert task.canRetry(3) == false;
        
        // 测试超过最大重试次数
        task.setRetryCount(5);
        assert task.canRetry(3) == false;
    }
}
